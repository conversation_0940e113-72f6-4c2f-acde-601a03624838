# 副本日历插件

这是一个为乌龟魔兽世界提供副本日历和副本进度追踪功能的插件，支持拉风、卡拉赞、血环三个服务器。
开发此插件的初衷是即为了方便查看副本和战场等更新日期，还为了给自己的多账号角色进行副本信息同步，方便只用1个角色的时候就可以看到所有角色的副本信息。

## 安装方法

1. 下载插件文件
2. 解压到你的 `魔兽世界/Interface/AddOns/` 目录下
3. 确保文件夹结构如下：
   ```
   魔兽世界/Interface/AddOns/RaidCalendar/
   ├── RaidCalendar.lua
   ├── RaidCalendar.toc
   ├── RaidRecords copy.lua
   ├── ProfessionCooldowns.lua
   ├── Whitelist.lua
   ├── UI.xml
   └── textures/ (专业图标文件夹)
   ```

## 使用方法

- **打开/关闭界面**：在聊天框输入 `/rc` 或点击小地图按钮
- **副本记录窗口**：左键小地图按钮打开主界面，右键打开副本记录独立窗口
- 主窗口显示带有副本重置日标记的日历
- 右侧面板显示你的角色和公会成员的副本锁定信息以及专业技能冷却状态

## 功能特色

### 日历功能
- **每周重置追踪**：显示标准的周三副本重置日
- **特殊重置追踪**：追踪5天重置的奥妮克希亚和卡拉赞
- **3天重置追踪**：追踪祖尔格拉布和安其拉废墟的3天重置
- **战场轮换**：显示每日战场轮换信息
- **小地图按钮**：快速访问日历界面

### 副本记录功能
- **角色副本追踪**：显示你角色的已保存副本
- **公会同步**：与公会成员同步副本进度信息
- **智能数据清理**：自动清理过期副本数据
- **颜色标识**：相同副本ID的玩家用相同颜色标识
- **独立窗口**：可独立打开副本记录窗口

### 专业技能冷却功能 (新增)
- **炼金术转化**：追踪奥金转化24小时冷却
- **裁缝月布**：追踪月布制作96小时冷却  
- **制皮精炼**：追踪筛盐器3天冷却
- **工会同步**：通过工会备注同步专业状态
- **智能通知**：技能可用时屏幕提示+声音提醒
- **职业颜色**：按职业颜色显示角色名称

## 最新更新 (v3.1.0) - 多服务器支持版

本次更新实现了多服务器数据分离和代码优化：

### 重大架构升级
- **多服务器支持**：完全重构数据存储结构
  - 支持拉风(Ravenstorm)、卡拉赞(Karazhan)、血环(Blood Ring)三个服务器
  - 按服务器分类存储副本数据，完全隔离不同服务器的信息
  - 自动检测当前服务器并切换到对应数据
  - 智能数据迁移：自动将旧数据迁移到新的服务器分类结构

- **数据结构重构**：
  ```lua
  RaidCalendarDB = {
      ["Ravenstorm"] = {
          ["characters"] = { ... },
          ["guildRaids"] = { ... }
      },
      ["Karazhan"] = { ... },
      ["Blood Ring"] = { ... }
  }
  ```

### 代码质量提升
- **大规模代码清理**：删除了5个过时函数和大量无用代码
  - 移除旧的重置时间计算函数
  - 删除过时的数据清理函数
  - 清理无意义的代码片段和注释
- **数据访问统一**：修复了6处数据结构引用，确保所有功能都使用新的服务器分类结构
- **同步机制优化**：工会同步、登录同步、智能回复等功能完全适配新结构

### 功能完善
- **服务器隔离**：不同服务器的角色副本信息完全分离，避免数据混乱
- **自动迁移**：首次使用时自动将旧数据迁移到拉风服务器分类下
- **兼容性保持**：保持所有原有功能，用户无需重新配置

---

## 历史更新 (v2.0.0) - 模块化重构版

v2.0.0版本实现了完整的模块化重构和专业技能管理：

### 新增功能
- **专业技能冷却模块**：全新的专业技能管理系统
  - 支持炼金术、裁缝、制皮三大专业
  - 自动检测已学专业并优先排序
  - 工会备注静默同步机制
  - 智能通知系统（10分钟间隔提醒）
  - 职业颜色角色名显示

- **模块化架构**：
  - 副本记录模块独立化 (RaidRecords copy.lua)
  - 专业技能模块独立化 (ProfessionCooldowns.lua)
  - 独立的事件处理和命令系统
  - 完整的文件级注释文档

- **数据管理优化**：
  - 智能过期数据清理系统
  - 按副本类型的重置周期管理
  - 角色级别数据存储优化
  - 防重复同步机制

### 界面改进
- **独立右侧窗口**：副本记录和专业技能可独立显示
- **智能窗口调整**：根据内容自动调整窗口大小
- **动态排序**：已学专业/当前角色优先显示
- **展开收缩**：可展开查看详细信息
- **实时更新**：5分钟定时同步更新

### 技术优化
- **代码结构重构**：移除重复代码，优化函数定义顺序
- **错误处理增强**：安全的函数调用和类型检查
- **性能优化**：缓存机制和延迟加载
- **兼容性提升**：更好的API兼容性检查

## 使用说明

### 基础操作
1. **首次使用**：插件会自动检测你的副本锁定和专业技能
2. **公会同步**：确保你在公会中且角色在白名单内才能同步数据
3. **小队/团队同步**：加入小队或团队时，白名单成员间会自动同步
4. **查看进度**：右侧面板显示所有成员的副本进度和专业状态

### 专业技能功能
1. **自动检测**：登录时自动检测已学专业
2. **冷却追踪**：实时显示专业技能冷却时间
3. **工会同步**：通过官员备注同步专业状态（静默模式）
4. **智能提醒**：技能可用时屏幕提示和声音提醒
5. **职业显示**：按职业颜色显示角色名称

### 工会备注格式
- `MMDDHHMI转1234` - 月日时分+转化技能+剩余时间(HHMM)
- `MMDDHHMI月可` - 月日时分+月布技能可用
- `MMDDHHMI盐7200` - 月日时分+筛盐器+剩余秒数

## 命令列表

- `/rc` - 打开/关闭主界面
- `/rc right` 或 `/rc record` - 打开/关闭副本记录窗口
- `/rc save` - 手动保存副本数据
- `/rc clearall` - 清理所有副本数据
- `/rc debug` - 调试专业检测
- `/rc debugdata` - 调试专业冷却数据

## 已知问题

- 公会同步需要在公会中才能使用
- 小队/团队同步需要所有参与者都在白名单中
- 角色需要在白名单中才能发送/接收数据
- 制皮专业需要背包中有筛盐器才能检测冷却

## 作者

拉风乌龟

## 版本历史

- **v3.1.0** - 多服务器支持，数据结构重构，代码优化
- **v2.0.0** - 模块化重构，新增专业技能管理
- **v1.1.8** - 数据同步优化和界面改进
- **v1.0.8** - 基础功能实现

## 许可证

未经作者许可不得私下贩卖。