

-- 修改：8位明文时间戳函数（移除年份）
function RaidCalendar_GetReadableTimestamp()
    local timeTable = date("*t", time())
    local month = timeTable.month
    local day = timeTable.day
    local hour = timeTable.hour
    local min = timeTable.min
    
    -- 确保各部分都在合理范围内
    if month < 1 or month > 12 then month = 1 end
    if day < 1 or day > 31 then day = 1 end
    if hour < 0 or hour > 23 then hour = 0 end
    if min < 0 or min > 59 then min = 0 end
    
    -- 8位格式：MMDDHHMM (月日时分)
    return month * 1000000 + day * 10000 + hour * 100 + min
end

-- 修改：8位明文时间戳转Unix时间戳
function RaidCalendar_ReadableToUnixTimestamp(readableTimestamp)
    -- 检查输入是否有效（8位数字范围）
    if not readableTimestamp or type(readableTimestamp) ~= "number" or readableTimestamp <= 0 then
        return time() -- 返回当前时间
    end
    
    -- 防止数值过大或过小
    if readableTimestamp > 99999999 or readableTimestamp < 1000000 then
        return time() -- 返回当前时间
    end
    
    -- 解析8位时间戳：MMDDHHMM
    local month = math.floor(readableTimestamp / 1000000)
    local remainder = math.mod(readableTimestamp, 1000000)
    local day = math.floor(remainder / 10000)
    remainder = math.mod(remainder, 10000)
    local hour = math.floor(remainder / 100)
    local min = math.mod(remainder, 100)
    
    -- 验证各部分是否在合理范围内
    if month < 1 or month > 12 or day < 1 or day > 31 or 
       hour < 0 or hour > 23 or min < 0 or min > 59 then
        return time() -- 返回当前时间
    end
    
    -- 使用当前年份创建时间戳
    local currentYear = date("*t", time()).year
    
    -- 安全创建时间戳
    local success, result = pcall(time, {
        year = currentYear,
        month = month,
        day = day,
        hour = hour,
        min = min,
        sec = 0
    })
    
    if success and result then
        return result
    else
        return time() -- 如果创建失败，返回当前时间
    end
end

-- 新增：从resetTime字符串中提取日期部分（移除时间）
function RaidCalendar_ExtractDateFromResetTime(resetTimeStr)
    if not resetTimeStr or resetTimeStr == "未知" then
        return "未知"
    end

    -- 提取日期部分：X月Y日 HH:MM -> X月Y日
    local dateOnly = string.match(resetTimeStr, "(%d+月%d+日)")
    if dateOnly then
        return dateOnly
    end

    -- 如果匹配失败，返回原字符串
    return resetTimeStr
end



-- 新增：解析resetTime字符串格式（如 "7月18日 12:00"）为时间戳
function RaidCalendar_ParseResetTimeString(resetTimeStr)
    if not resetTimeStr or resetTimeStr == "未知" then
        return nil
    end

    -- 解析格式：X月Y日 HH:MM
    local month, day, hour, minute = string.match(resetTimeStr, "(%d+)月(%d+)日 (%d+):(%d+)")
    if not month or not day or not hour or not minute then
        return nil
    end

    month = tonumber(month)
    day = tonumber(day)
    hour = tonumber(hour)
    minute = tonumber(minute)

    if not month or not day or not hour or not minute then
        return nil
    end

    -- 构建时间表
    local currentDate = date("*t", time())
    local resetDate = {
        year = currentDate.year,
        month = month,
        day = day,
        hour = hour,
        min = minute,
        sec = 0
    }

    -- 副本重置时间不会跨年，直接使用当前年份

    return time(resetDate)
end



-- 修复：检查其他人的副本数据是否过期，正确处理resetTime字符串格式
function RaidCalendar_IsRaidDataExpired(raidInfo)
    if not raidInfo then
        return true -- 数据不存在，认为已过期
    end

    local currentTime = time()

    -- 优先使用reset字段（秒数）进行精确计算
    if raidInfo.reset and raidInfo.reset > 0 then
        local recordTime = raidInfo.readableTime and RaidCalendar_ReadableToUnixTimestamp(raidInfo.readableTime) or currentTime
        local resetTime = recordTime + raidInfo.reset
        return currentTime >= resetTime
    end

    -- 如果没有reset字段，尝试解析resetTime字符串
    if raidInfo.resetTime and raidInfo.resetTime ~= "未知" then
        local resetTimestamp = RaidCalendar_ParseResetTimeString(raidInfo.resetTime)
        if resetTimestamp then
            return currentTime >= resetTimestamp
        end
        -- 如果解析失败，保守处理：不认为过期
        return false
    end

    -- 既没有reset也没有resetTime，认为过期
    return true
end

-- 自动清理过期副本数据
function RaidCalendar_AutoCleanupExpiredRaidData()
    if not RaidCalendarDB then
        return 0
    end

    local cleanupCount = 0
    local currentPlayerName = UnitName("player")

    -- 清理同账号其他角色的过期副本数据（所有服务器）
    for serverName, serverData in pairs(RaidCalendarDB) do
        if type(serverData) == "table" and serverData.characters then
            for characterName, characterData in pairs(serverData.characters) do
            -- 跳过当前角色（当前角色的数据每次登录都会重新获取）
            if characterName ~= currentPlayerName and characterData and characterData.raids then
                local cleanRaids = {}

                for _, raidInfo in ipairs(characterData.raids) do
                    if raidInfo.readableTime then
                        -- 临时设置name字段用于过期检查
                        raidInfo.name = raidInfo.name or "未知副本"

                        if not RaidCalendar_IsRaidDataExpired(raidInfo) then
                            table.insert(cleanRaids, raidInfo)
                        else
                            cleanupCount = cleanupCount + 1
                        end
                    else
                        cleanupCount = cleanupCount + 1
                    end
                end

                characterData.raids = cleanRaids
            end
        end
        end  -- 对应serverData.characters的end
    end  -- 对应for serverName的end

    -- 清理工会数据中的过期副本（所有服务器）
    for serverName, serverData in pairs(RaidCalendarDB) do
        if type(serverData) == "table" and serverData.guildRaids then
            for playerName, playerRaids in pairs(serverData.guildRaids) do
            if type(playerRaids) == "table" then
                for raidName, raidEntries in pairs(playerRaids) do
                    if type(raidEntries) == "table" then
                        local cleanEntries = {}
                        
                        for _, raidInfo in ipairs(raidEntries) do
                            if raidInfo.readableTime then
                                -- 内存优化：直接传递必要参数，避免创建临时对象
                                -- 临时设置name字段用于过期检查
                                raidInfo.name = raidName

                                if not RaidCalendar_IsRaidDataExpired(raidInfo) then
                                    -- 清理临时字段，避免污染数据
                                    raidInfo.name = nil
                                    table.insert(cleanEntries, raidInfo)
                                else
                                    raidInfo.name = nil
                                    cleanupCount = cleanupCount + 1
                                end
                            else
                                cleanupCount = cleanupCount + 1
                            end
                        end
                        
                        if next(cleanEntries) ~= nil then
                            playerRaids[raidName] = cleanEntries
                        else
                            playerRaids[raidName] = nil
                        end
                    end
                end
                
                if next(playerRaids) == nil then
                    serverData.guildRaids[playerName] = nil
                end
            end
        end
        end  -- 对应serverData.guildRaids的end
    end  -- 对应for serverName的end
    
    -- 静默清理，不输出调试信息
    return cleanupCount
end


-- 修改：队列处理，添加队列大小限制
function RaidCalendar_ProcessLoginSyncQueue()
    if RaidCalendar.globalSyncLimiter.isProcessingQueue then
        return
    end

    local queue = RaidCalendar.globalSyncLimiter.loginSyncQueue
    if not queue or table.getn(queue) == 0 then
        return
    end

    -- 限制队列大小，防止内存泄露
    local maxQueueSize = 5
    if table.getn(queue) > maxQueueSize then
        local newQueue = {}
        local startIndex = table.getn(queue) - maxQueueSize + 1
        for i = startIndex, table.getn(queue) do
            table.insert(newQueue, queue[i])
        end
        RaidCalendar.globalSyncLimiter.loginSyncQueue = newQueue
        queue = newQueue
    end

    RaidCalendar.globalSyncLimiter.isProcessingQueue = true
    
    -- 每次只处理一个，间隔30秒
    local processInterval = 30 -- 30秒间隔
    local currentIndex = 1

    local function processNext()
        if currentIndex <= table.getn(queue) then
            local syncData = queue[currentIndex]

            if syncData and syncData.playerName and syncData.message then
                -- 发送消息
                if SendAddonMessage and RaidCalendar.MSG_PREFIX then
                    SendAddonMessage(RaidCalendar.MSG_PREFIX, syncData.message, "GUILD")
                end
            end

            currentIndex = currentIndex + 1

            -- 继续下一个
            if currentIndex <= table.getn(queue) then
                -- 内存优化：使用单一Frame对象，避免重复创建
                if not RaidCalendar.globalSyncLimiter.processFrame then
                    RaidCalendar.globalSyncLimiter.processFrame = CreateFrame("Frame")
                end

                local processFrame = RaidCalendar.globalSyncLimiter.processFrame
                local nextElapsed = 0
                processFrame:SetScript("OnUpdate", function()
                    nextElapsed = nextElapsed + arg1
                    if nextElapsed >= processInterval then
                        processFrame:SetScript("OnUpdate", nil)
                        processNext()
                    end
                end)
            else
                -- 队列处理完成，清理资源
                RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                RaidCalendar.globalSyncLimiter.isProcessingQueue = false

                -- 内存优化：清理Frame引用
                if RaidCalendar.globalSyncLimiter.processFrame then
                    RaidCalendar.globalSyncLimiter.processFrame:SetScript("OnUpdate", nil)
                end
            end
        end
    end

    -- 开始处理第一个
    processNext()
end

function RaidCalendar_CreateRightPanel(parentFrame)
    local playerNameDisplay = GetUnitName("player") or "未知角色"
    local localizedPlayerClass, playerClassToken = UnitClass("player")
    local hexColor = RaidCalendar_GetClassColorHex(playerClassToken)
    
    -- 添加副本记录标题
    local raidTitle = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    raidTitle:SetPoint("TOPLEFT", parentFrame, "TOPLEFT", 75, -20)
    raidTitle:SetText("副本记录")
    raidTitle:SetTextColor(1, 1, 0)
    raidTitle.elementType = "raidRecord"
    
    -- 将标题保存到父窗口以便后续引用
    parentFrame.raidTitle = raidTitle

    local charInfo = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    charInfo:SetPoint("TOP", raidTitle, "BOTTOM", 0, -15)  -- 锚定副本记录标题，居中对齐，向下15像素
    charInfo:SetText("角色: |cFF" .. hexColor .. playerNameDisplay .. "|r (" .. localizedPlayerClass .. ")")
    charInfo.elementType = "raidRecord"

    local serverName = GetRealmName() or "未知服务器"
    local serverInfo = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    serverInfo:SetPoint("TOP", charInfo, "BOTTOM", 0, -5)  -- 相对于角色信息居中
    serverInfo:SetText("服务器: " .. serverName)
    serverInfo:SetTextColor(0.8, 0.8, 1)
    serverInfo.elementType = "raidRecord"

    parentFrame.raidElements = {}
    
    RaidCalendar_UpdateRaidInfo(parentFrame)
end

function RaidCalendar_UpdateRaidInfo(parentFrame)
    if not parentFrame then
        return
    end

    -- 内存优化：清理旧元素时避免重复检查
    if parentFrame.raidElements then
        for i = 1, table.getn(parentFrame.raidElements) do
            local element = parentFrame.raidElements[i]
            if element and element.Hide then
                element:Hide()
            end
            parentFrame.raidElements[i] = nil -- 显式清理引用
        end
    end
    parentFrame.raidElements = {}

    local yOffset = -110
    local hasRaids = false
    local raidGroups = {}

    if not parentFrame.raidGroupStates then
        parentFrame.raidGroupStates = {}
    end

    -- 获取当前服务器名称（直接实现，不依赖主文件）
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    -- 内存优化：缓存常用的玩家信息，避免重复调用
    local currentPlayerName = GetUnitName("player")
    local _, currentPlayerClass = UnitClass("player")
    
    if GetNumSavedInstances then
        local numSavedInstances = GetNumSavedInstances()
        if numSavedInstances and numSavedInstances > 0 then
            for i = 1, numSavedInstances do
                local instanceName, instanceID, instanceReset = GetSavedInstanceInfo(i)
                if instanceName then
                    if not raidGroups[instanceName] then
                        raidGroups[instanceName] = { players = {}, expanded = false }
                    end

                    -- 计算重置时间（修复：确保显示整点时间）
                    local resetTimeText = "未知"
                    if instanceReset and instanceReset > 0 then
                        local resetTime = time() + instanceReset
                        local resetTable = date("*t", resetTime)
                        -- 修复：副本重置时间应该是整点，强制设置为12:00
                        resetTable.min = 0
                        resetTable.sec = 0
                        resetTimeText = string.format("%d月%d日 %02d:%02d",
                            resetTable.month, resetTable.day, resetTable.hour, resetTable.min)
                    end

                    table.insert(raidGroups[instanceName].players, {
                        name = currentPlayerName or "未知角色",
                        id = instanceID or "未知",
                        resetTime = resetTimeText,  -- 添加重置时间
                        isCurrent = true,
                        class = currentPlayerClass
                    })
                end
            end
        end
    end

    -- 读取本账号其他角色数据（当前服务器）
    if RaidCalendarDB and RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].characters then
        for characterName, characterData in pairs(RaidCalendarDB[currentServer].characters) do
            -- 跳过当前角色（当前角色的数据已经通过GetSavedInstanceInfo获取）
            if characterName ~= currentPlayerName and characterData and characterData.raids then
                for _, raidInfo in ipairs(characterData.raids) do
                    if raidInfo.name and raidInfo.id then
                        if not raidGroups[raidInfo.name] then
                            raidGroups[raidInfo.name] = { players = {}, expanded = false }
                        end

                        -- 内存优化：尝试获取角色职业信息，使用缓存的当前角色信息
                        local characterClass = "UNKNOWN"
                        if characterData.class then
                            characterClass = characterData.class
                        elseif characterName == currentPlayerName then
                            -- 如果是当前角色，使用缓存的职业信息
                            characterClass = currentPlayerClass or "UNKNOWN"
                        end

                        table.insert(raidGroups[raidInfo.name].players, {
                            name = characterName,
                            id = raidInfo.id,
                            resetTime = raidInfo.resetTime or "未知",
                            isCurrent = false,
                            isAccountCharacter = true,  -- 标记为本账号角色
                            class = characterClass
                        })
                    end
                end
            end
        end
    end

    -- 读取工会同步数据（当前服务器）
    if RaidCalendarDB and RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].guildRaids then
        for characterName, characterGuildData in pairs(RaidCalendarDB[currentServer].guildRaids) do
            -- 检查是否是本账号的角色，避免重复显示
            local isAccountCharacter = false
            if RaidCalendarDB[currentServer].characters and RaidCalendarDB[currentServer].characters[characterName] then
                isAccountCharacter = true
            end

            if not isAccountCharacter then
                if type(characterGuildData) == "table" then
                    for raidName, raidEntries in pairs(characterGuildData) do
                        if type(raidEntries) == "table" then
                            for _, raidInfo in ipairs(raidEntries) do
                                if raidName and raidInfo.id then
                                    if not raidGroups[raidName] then
                                        raidGroups[raidName] = { players = {}, expanded = false }
                                    end
                                    table.insert(raidGroups[raidName].players, {
                                        name = characterName,  -- 使用角色名而不是发送者名
                                        id = raidInfo.id,
                                        resetTime = raidInfo.resetTime or "未知",
                                        isCurrent = false,
                                        class = raidInfo.class
                                    })
                                end
                            end
                        end
                    end
                end
            end
        end
    end

    for raidName, raidGroup in pairs(raidGroups) do
        hasRaids = true
        
        if parentFrame.raidGroupStates[raidName] ~= nil then
            raidGroup.expanded = parentFrame.raidGroupStates[raidName]
        else
            raidGroup.expanded = false
            parentFrame.raidGroupStates[raidName] = false
        end
        
        if raidGroup.players then
            table.sort(raidGroup.players, function(a, b)
                -- 优先级1: 当前角色排在最前面
                if a.isCurrent and not b.isCurrent then
                    return true -- a是当前角色，b不是，a排前面
                elseif not a.isCurrent and b.isCurrent then
                    return false -- b是当前角色，a不是，b排前面
                end
                
                -- 优先级2: 如果都是当前角色或都不是当前角色，按副本ID排序
                -- 但要考虑当前角色的ID，让相同ID的角色聚集在一起
                local currentPlayerID = nil
                for _, playerInfo in ipairs(raidGroup.players) do
                    if playerInfo.isCurrent then
                        currentPlayerID = playerInfo.id
                        break
                    end
                end
                
                if currentPlayerID then
                    -- 如果有当前角色，优先显示与当前角色相同ID的其他角色
                    local aIsCurrentID = (tostring(a.id) == tostring(currentPlayerID))
                    local bIsCurrentID = (tostring(b.id) == tostring(currentPlayerID))
                    
                    if aIsCurrentID and not bIsCurrentID then
                        return true -- a与当前角色同ID，b不是，a排前面
                    elseif not aIsCurrentID and bIsCurrentID then
                        return false -- b与当前角色同ID，a不是，b排前面
                    end
                end
                
                -- 优先级3: 如果ID关系相同，按副本ID数字排序
                if a.id ~= b.id then
                    return tostring(a.id) < tostring(b.id)
                end
                
                -- 优先级4: 如果副本ID相同，按角色名排序
                return a.name < b.name
            end)
        end
        
        local idCounts = {}
        if raidGroup.players then
            for _, playerInfo in ipairs(raidGroup.players) do
                if playerInfo.id then
                    idCounts[playerInfo.id] = (idCounts[playerInfo.id] or 0) + 1
                end
            end
        end

        local instanceIdToColorMap = {}
        local nextSharedColorIndex = 1
        
        local expandButton = CreateFrame("Button", "RaidGroupExpand_" .. raidName, parentFrame)
        expandButton:SetPoint("TOPLEFT", parentFrame, "TOPLEFT", 15, yOffset)  -- 锚定左上角，距离左边界15像素
        expandButton:SetWidth(180)
        expandButton:SetHeight(20)
        
        local hasCurrentPlayer = false
        for _, playerInfo in ipairs(raidGroup.players) do
            if playerInfo.isCurrent then
                hasCurrentPlayer = true
                break
            end
        end
        
        local buttonBg = expandButton:CreateTexture(nil, "BACKGROUND")
        buttonBg:SetAllPoints(expandButton)
        if hasCurrentPlayer then
            buttonBg:SetTexture(0.8, 0.3, 0.3, 0.5)
        else
            buttonBg:SetTexture(0.2, 0.2, 0.2, 0.5)
        end

        expandButton:SetBackdrop({
          edgeFile = "Interface\\Buttons\\WHITE8X8",
          edgeSize = 1,
        })
        expandButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        expandButton:SetBackdropColor(0, 0, 0, 0)
        
        local raidNameText = expandButton:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
        raidNameText:SetPoint("LEFT", expandButton, "LEFT", 50, 0)
        local playerCount = table.getn(raidGroup.players)
        raidNameText:SetText(raidName .. " (" .. playerCount .. "人)")
        
        local expandIcon = expandButton:CreateTexture(nil, "OVERLAY")
        expandIcon:SetPoint("LEFT", expandButton, "LEFT", 5, 0)
        expandIcon:SetWidth(16)
        expandIcon:SetHeight(16)
        
        if raidGroup.expanded then
            expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
        else
            expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
        end
        
        local raidIcon = expandButton:CreateTexture(nil, "OVERLAY")
        raidIcon:SetPoint("LEFT", expandButton, "LEFT", 25, 0)
        raidIcon:SetWidth(20)
        raidIcon:SetHeight(20)
        
        local iconPath = RaidCalendar_GetRaidIcon(raidName)
        raidIcon:SetTexture(iconPath)
        
        expandButton.expandIcon = expandIcon
        expandButton.raidIcon = raidIcon

        expandButton.expanded = raidGroup.expanded
        expandButton.raidName = raidName
        expandButton.players = raidGroup.players
        expandButton.nameText = raidNameText
        expandButton.parentFrame = parentFrame
        
        -- 内存优化：使用简化的Frame名称，避免字符串拼接
        local detailFrame = CreateFrame("Frame", nil, parentFrame)
        detailFrame:SetPoint("TOPLEFT", expandButton, "BOTTOMLEFT", 0, -7)
        detailFrame:SetWidth(180)

        local maxVisiblePlayers = 10
        local playerDisplayHeight = math.min(playerCount, maxVisiblePlayers) * 18
        detailFrame:SetHeight(playerDisplayHeight)

        expandButton.detailFrame = detailFrame
        detailFrame:Hide()

        local scrollFrame = nil
        local scrollChild = nil
        local scrollBar = nil

        if playerCount > maxVisiblePlayers then
            -- 内存优化：不使用全局名称，减少内存占用
            scrollFrame = CreateFrame("ScrollFrame", nil, detailFrame)
            scrollFrame:SetPoint("TOPLEFT", detailFrame, "TOPLEFT", 0, 0)
            scrollFrame:SetPoint("BOTTOMRIGHT", detailFrame, "BOTTOMRIGHT", -16, 0)

            scrollChild = CreateFrame("Frame", nil, scrollFrame)
            scrollChild:SetWidth(184)
            scrollChild:SetHeight(playerCount * 18)
            scrollFrame:SetScrollChild(scrollChild)
            
            -- 内存优化：简化滚动条创建，不使用全局名称
            scrollBar = CreateFrame("Slider", nil, detailFrame)
            scrollBar:SetPoint("TOPRIGHT", detailFrame, "TOPRIGHT", 0, 0)
            scrollBar:SetPoint("BOTTOMRIGHT", detailFrame, "BOTTOMRIGHT", 0, 0)
            scrollBar:SetWidth(16)
            scrollBar:SetOrientation("VERTICAL")
            scrollBar:SetMinMaxValues(0, math.max(0, playerCount * 18 - playerDisplayHeight))
            scrollBar:SetValueStep(18)
            scrollBar:SetValue(0)

            -- 内存优化：简化纹理创建
            local scrollBg = scrollBar:CreateTexture(nil, "BACKGROUND")
            scrollBg:SetAllPoints(scrollBar)
            scrollBg:SetTexture(0.2, 0.2, 0.2, 0.8)

            local scrollThumb = scrollBar:CreateTexture(nil, "OVERLAY")
            scrollThumb:SetWidth(16)
            scrollThumb:SetHeight(20)
            scrollThumb:SetTexture(0.6, 0.6, 0.6, 1)
            scrollBar:SetThumbTexture(scrollThumb)
            
            scrollBar:SetScript("OnValueChanged", function()
                local value = this:GetValue()
                scrollFrame:SetVerticalScroll(value)
            end)
            
            scrollFrame:EnableMouseWheel(true)
            scrollFrame:SetScript("OnMouseWheel", function()
                local delta = arg1
                local newValue = scrollBar:GetValue() - (delta * 18)
                local minVal, maxVal = scrollBar:GetMinMaxValues()
                newValue = math.max(minVal, math.min(maxVal, newValue))
                scrollBar:SetValue(newValue)
            end)
            
            detailFrame.scrollFrame = scrollFrame
            detailFrame.scrollChild = scrollChild
            detailFrame.scrollBar = scrollBar
        end
        
        local playerYOffset = 0
        local playerParent = scrollChild or detailFrame
        
        for _, playerInfo in ipairs(raidGroup.players) do
            local playerText = playerParent:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
            playerText:SetPoint("TOPLEFT", playerParent, "TOPLEFT", 0, -playerYOffset)  -- 从35改为15，向左移动20像素
            
            local playerClassToken = playerInfo.class
            local hexColor = RaidCalendar_GetClassColorHex(playerClassToken)
            
            local instanceId = playerInfo.id or "未知"
            local idDisplayColorHex = "FFFFFF"

            if idCounts[instanceId] and idCounts[instanceId] > 1 then
                if instanceIdToColorMap[instanceId] then
                    idDisplayColorHex = instanceIdToColorMap[instanceId]
                else
                    if table.getn(RaidCalendar.sharedIdColors) > 0 then
                        idDisplayColorHex = RaidCalendar.sharedIdColors[nextSharedColorIndex]
                        instanceIdToColorMap[instanceId] = idDisplayColorHex
                        nextSharedColorIndex = nextSharedColorIndex + 1
                        if nextSharedColorIndex > table.getn(RaidCalendar.sharedIdColors) then
                            nextSharedColorIndex = 1
                        end
                    end
                end
            end
            
            -- 内存优化：减少字符串拼接操作，使用更高效的格式化
            local displayText
            if playerInfo.resetTime then
                -- 只显示日期，移除时间部分
                local dateOnly = RaidCalendar_ExtractDateFromResetTime(playerInfo.resetTime)
                displayText = string.format("|cFF%s%s|r ID: |cFF%s%s|r - |cFFFFFFFF%s|r",
                    hexColor, playerInfo.name, idDisplayColorHex, instanceId, dateOnly)
            else
                displayText = string.format("|cFF%s%s|r ID: |cFF%s%s|r",
                    hexColor, playerInfo.name, idDisplayColorHex, instanceId)
            end

            playerText:SetText(displayText)
            playerYOffset = playerYOffset + 18
        end
        
        -- 修复展开/收缩显示逻辑
        if raidGroup.expanded then
            detailFrame:Show()
        else
            detailFrame:Hide()
        end
        
        expandButton:SetScript("OnClick", function()
            local currentParentFrame = this.parentFrame
            
            if this.expanded then
                -- 收缩
                this.expanded = false
                currentParentFrame.raidGroupStates[this.raidName] = false
                if this.expandIcon then
                    this.expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
                end
                if this.detailFrame then
                    this.detailFrame:Hide()
                end
            else
                -- 先收缩其他所有展开的项目
                for stateName, _ in pairs(currentParentFrame.raidGroupStates) do
                    if stateName ~= this.raidName then
                        currentParentFrame.raidGroupStates[stateName] = false
                    end
                end
                
                -- 展开当前项目
                this.expanded = true
                currentParentFrame.raidGroupStates[this.raidName] = true
                if this.expandIcon then
                    this.expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
                end
                if this.detailFrame then
                    this.detailFrame:Show()
                end
            end
            
            -- 重新布局：需要重新计算所有元素位置，但保持展开状态
            RaidCalendar_RelayoutRaidElements(currentParentFrame)
        end)
        
        table.insert(parentFrame.raidElements, expandButton)
        table.insert(parentFrame.raidElements, detailFrame)
        
        if raidGroup.expanded then
            yOffset = yOffset - (25 + playerDisplayHeight)
        else
            yOffset = yOffset - 25
        end
    end
    
    if not hasRaids then
        local noRaidText = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        if parentFrame.raidTitle then
            noRaidText:SetPoint("TOP", parentFrame.raidTitle, "BOTTOM", 0, -80)  -- 向下70像素（原来是-60）
        else
            noRaidText:SetPoint("TOP", parentFrame, "TOP", 0, yOffset)  -- 备用锚点
        end
        noRaidText:SetText("当前无副本记录")
        noRaidText:SetTextColor(0.5, 0.5, 0.5)
        noRaidText.elementType = "raidRecord"
        table.insert(parentFrame.raidElements, noRaidText)
    end

end

-- 移入：共享ID颜色配置（从主文件移入）
RaidCalendar.sharedIdColors = {
    "FFFF00",
    "00FFFF", 
    "FF00FF",
    "FFA500",
    "32CD32",
    "FF69B4",
    "ADFF2F",
    "FF4500",
    "1E90FF",
}

-- 移入：副本图标映射表（从主文件移入）
RaidCalendar.raidIconMap = {
    ["熔火之心"] = "Interface\\AddOns\\RaidCalendar\\textures\\MC_Icon.tga",
    ["黑翼之巢"] = "Interface\\AddOns\\RaidCalendar\\textures\\BWL_Icon.tga",
    ["安其拉神殿"] = "Interface\\AddOns\\RaidCalendar\\textures\\TAQ_Icon.tga",
    ["纳克萨玛斯"] = "Interface\\AddOns\\RaidCalendar\\textures\\NAXX_Icon.tga",
    ["翡翠圣地"] = "Interface\\AddOns\\RaidCalendar\\textures\\FC_Icon.tga",
    ["卡拉赞之塔"] = "Interface\\AddOns\\RaidCalendar\\textures\\K40_Icon.tga",
    ["奥妮克希亚的巢穴"] = "Interface\\AddOns\\RaidCalendar\\textures\\HL_Icon.tga",
    ["卡拉赞"] = "Interface\\AddOns\\RaidCalendar\\textures\\KLZ_Icon.tga",
    ["祖尔格拉布"] = "Interface\\AddOns\\RaidCalendar\\textures\\ZG_Icon.tga",
    ["安其拉废墟"] = "Interface\\AddOns\\RaidCalendar\\textures\\AQ20_Icon.tga",
}

-- 移入：副本图标获取函数（从主文件移入）
function RaidCalendar_GetRaidIcon(raidName)
    if not raidName then
        return nil
    end
    
    if RaidCalendar.raidIconMap[raidName] then
        return RaidCalendar.raidIconMap[raidName]
    end
    
    for mapName, iconPath in pairs(RaidCalendar.raidIconMap) do
        if string.find(raidName, mapName) or string.find(mapName, raidName) then
            return iconPath
        end
    end
    
    return nil
end

-- 新增：强制清理错误的数据结构，支持新的服务器分类结构
function RaidCalendar_CleanupDataStructure()
    if not RaidCalendarDB then
        RaidCalendarDB = {}
        return
    end

    -- 检查是否是旧的数据结构，如果是则进行迁移
    if RaidCalendarDB.characters or RaidCalendarDB.guildRaids then
        -- 旧数据结构迁移到新结构
        local oldCharacters = RaidCalendarDB.characters or {}
        local oldGuildRaids = RaidCalendarDB.guildRaids or {}

        -- 清空旧结构
        RaidCalendarDB.characters = nil
        RaidCalendarDB.guildRaids = nil

        -- 迁移角色数据到默认服务器（Ravenstorm）
        if next(oldCharacters) then
            if not RaidCalendarDB["Ravenstorm"] then
                RaidCalendarDB["Ravenstorm"] = { characters = {}, guildRaids = {} }
            end
            for charName, charData in pairs(oldCharacters) do
                RaidCalendarDB["Ravenstorm"].characters[charName] = charData
                -- 移除旧的server字段，因为现在服务器信息在上一层
                if charData.server then
                    charData.server = nil
                end
            end
        end

        -- 迁移工会数据到默认服务器（Ravenstorm）
        if next(oldGuildRaids) then
            if not RaidCalendarDB["Ravenstorm"] then
                RaidCalendarDB["Ravenstorm"] = { characters = {}, guildRaids = {} }
            end
            RaidCalendarDB["Ravenstorm"].guildRaids = oldGuildRaids
        end

        return
    end

    -- 清理过时的数据结构
    if RaidCalendarDB.profile then
        RaidCalendarDB.profile = nil
    end

    if RaidCalendarDB.global then
        RaidCalendarDB.global = nil
    end

    -- 清理可能残留的旧顶级字段
    if RaidCalendarDB.characters then
        RaidCalendarDB.characters = nil
    end

    if RaidCalendarDB.guildRaids then
        RaidCalendarDB.guildRaids = nil
    end
end



function RaidCalendar_SaveCurrentCharacterRaids()
    local playerName = GetUnitName("player")
    
    if not playerName then
        return
    end
    
    -- 在保存前先清理数据结构（但不清理过期数据，交给定时系统处理）
    RaidCalendar_CleanupDataStructure()
    
    -- 记录保存前的数据哈希
    local oldHash = RaidCalendar_GetDataHash()
    
    -- 获取当前服务器名称（直接实现，不依赖主文件）
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    -- 确保数据库结构正确：服务器->characters/guildRaids
    if not RaidCalendarDB[currentServer] then
        RaidCalendarDB[currentServer] = {
            characters = {},
            guildRaids = {}
        }
    end

    if not RaidCalendarDB[currentServer].characters then
        RaidCalendarDB[currentServer].characters = {}
    end

    if not RaidCalendarDB[currentServer].guildRaids then
        RaidCalendarDB[currentServer].guildRaids = {}
    end

    -- 确保角色数据结构正确
    if not RaidCalendarDB[currentServer].characters[playerName] then
        local _, playerClassToken = UnitClass("player")
        RaidCalendarDB[currentServer].characters[playerName] = {
            name = playerName,
            raids = {},
            lastUpdate = RaidCalendar_GetReadableTimestamp(),
            class = playerClassToken or "UNKNOWN"
        }
    else
        -- 如果角色数据已存在但没有职业信息，补充职业信息
        if not RaidCalendarDB[currentServer].characters[playerName].class then
            local _, playerClassToken = UnitClass("player")
            RaidCalendarDB[currentServer].characters[playerName].class = playerClassToken or "UNKNOWN"
        end
    end
    
    local raids = {}
    
    -- 检查API可用性
    if not GetNumSavedInstances then
        return
    end
    
    local numSavedInstances = GetNumSavedInstances()

    if numSavedInstances and numSavedInstances > 0 then
        for i = 1, numSavedInstances do
            if GetSavedInstanceInfo then
                local instanceName, instanceID, instanceReset = GetSavedInstanceInfo(i)

                if instanceName and instanceID then
                    -- 计算重置时间（修复：确保显示整点时间）
                    local resetTimeText = "未知"
                    if instanceReset and instanceReset > 0 then
                        local resetTime = time() + instanceReset
                        local resetTable = date("*t", resetTime)
                        -- 修复：副本重置时间应该是整点，强制设置为12:00
                        resetTable.min = 0
                        resetTable.sec = 0
                        resetTimeText = string.format("%d月%d日 %02d:%02d",
                            resetTable.month, resetTable.day, resetTable.hour, resetTable.min)
                    end

                    table.insert(raids, {
                        name = instanceName,
                        id = instanceID,
                        reset = instanceReset or 0,  -- 保存重置秒数用于计算
                        resetTime = resetTimeText,   -- 可读的重置时间
                        readableTime = RaidCalendar_GetReadableTimestamp() -- 记录时间戳
                    })
                end
            else
                break
            end
        end
    end
    
    -- 重要修复：只更新当前角色的数据，不要覆盖其他角色的数据
    if playerName and RaidCalendarDB[currentServer].characters[playerName] then
        RaidCalendarDB[currentServer].characters[playerName].raids = raids
        RaidCalendarDB[currentServer].characters[playerName].lastUpdate = RaidCalendar_GetReadableTimestamp()
    end
    
    -- 检查数据是否有变化，如有变化则主动同步
    local newHash = RaidCalendar_GetDataHash()
    if oldHash ~= newHash then
        RaidCalendar.lastDataHash = newHash
        
        -- 延迟一小段时间再同步，确保数据保存完成
        local syncFrame = CreateFrame("Frame")
        local syncElapsed = 0
        syncFrame:SetScript("OnUpdate", function()
            syncElapsed = syncElapsed + arg1
            if syncElapsed >= 1 then -- 延迟1秒
                syncFrame:SetScript("OnUpdate", nil)
                RaidCalendar_CheckDataChangeAndSync()
            end
        end)
    end
end

function RaidCalendar_SyncToGuild()
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not IsInGuild() then
        return
    end
    
    -- 保存当前角色数据
    RaidCalendar_SaveCurrentCharacterRaids()
    
    -- 使用通用同步函数
    RaidCalendar_SendSyncMessage("GUILD")
end

-- 多部分消息缓存（内存保护）
if not RaidCalendar then RaidCalendar = {} end
if not RaidCalendar.multipartMessages then
    RaidCalendar.multipartMessages = {}
end

-- 内存保护配置
local MULTIPART_CONFIG = {
    maxConcurrentMessages = 10,  -- 最多同时处理10条消息
    messageTimeout = 30,         -- 30秒超时
    maxMessageSize = 5000,       -- 最大消息5KB
    cleanupInterval = 15         -- 15秒清理一次
}

-- 清理过期的多部分消息
local function CleanupMultipartMessages()
    local currentTime = time()
    local cleanedCount = 0

    for messageId, data in pairs(RaidCalendar.multipartMessages) do
        if not data.startTime then
            data.startTime = currentTime
        end

        -- 清理超时消息
        if currentTime - data.startTime > MULTIPART_CONFIG.messageTimeout then
            RaidCalendar.multipartMessages[messageId] = nil
            cleanedCount = cleanedCount + 1
        end
    end

    -- 如果缓存过多，清理最老的
    local messageCount = 0
    for _ in pairs(RaidCalendar.multipartMessages) do
        messageCount = messageCount + 1
    end

    if messageCount > MULTIPART_CONFIG.maxConcurrentMessages then
        local oldestId = nil
        local oldestTime = currentTime

        for messageId, data in pairs(RaidCalendar.multipartMessages) do
            if data.startTime and data.startTime < oldestTime then
                oldestTime = data.startTime
                oldestId = messageId
            end
        end

        if oldestId then
            RaidCalendar.multipartMessages[oldestId] = nil
            cleanedCount = cleanedCount + 1
        end
    end

    -- 静默清理，不输出调试信息
end

function RaidCalendar_ChatMsgAddonHandler(prefix, message, channel, sender)
    if prefix ~= RaidCalendar.MSG_PREFIX then
        return
    end

    if string.len(message) == 0 then
        return
    end

    -- 处理多部分消息
    if string.find(message, "^MULTIPART_START:") then
        local _, _, messageId, totalParts = string.find(message, "^MULTIPART_START:([^:]+):(%d+)")
        if messageId and totalParts then
            -- 内存保护：清理过期消息
            CleanupMultipartMessages()

            -- 检查消息大小限制
            local estimatedSize = tonumber(totalParts) * 200
            if estimatedSize > MULTIPART_CONFIG.maxMessageSize then
                return
            end

            RaidCalendar.multipartMessages[messageId] = {
                sender = sender,
                totalParts = tonumber(totalParts),
                receivedParts = {},
                receivedCount = 0,
                startTime = time()
            }
        end
        return
    elseif string.find(message, "^MULTIPART_DATA:") then
        local _, _, messageId, partNum, partData = string.find(message, "^MULTIPART_DATA:([^:]+):(%d+):(.*)")
        if messageId and partNum and partData then
            local multipartData = RaidCalendar.multipartMessages[messageId]
            if multipartData and multipartData.sender == sender then
                multipartData.receivedParts[tonumber(partNum)] = partData
                multipartData.receivedCount = multipartData.receivedCount + 1
            end
        end
        return
    elseif string.find(message, "^MULTIPART_END:") then
        local _, _, messageId = string.find(message, "^MULTIPART_END:([^:]+)")
        if messageId then
            local multipartData = RaidCalendar.multipartMessages[messageId]
            if multipartData and multipartData.sender == sender and multipartData.receivedCount == multipartData.totalParts then
                -- 重组完整消息
                local fullMessage = ""
                for i = 1, multipartData.totalParts do
                    fullMessage = fullMessage .. (multipartData.receivedParts[i] or "")
                end

                -- 清理缓存
                RaidCalendar.multipartMessages[messageId] = nil

                -- 递归处理完整消息
                message = fullMessage
            else
                return
            end
        end
    end

    -- 只处理工会频道消息
    if channel ~= "GUILD" then
        print("[RaidCalendar] 忽略非工会频道消息")
        return
    end
    
    -- 确保交换系统已初始化
    RaidCalendar_InitExchangeSystem()
    
    -- 严格的白名单检查：发送者必须在白名单中
    if not RaidCalendar_IsCharacterInWhitelist(sender) then
        return
    end
    
    -- 额外检查：接收者（当前角色）也必须在白名单中才能处理消息
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not RaidCalendarDB then
        RaidCalendarDB = {}
    end

    -- 简化消息处理：移除回复消息检查
    local actualMessage = message
    if string.sub(message, 1, 6) == "REPLY:" then
        actualMessage = string.sub(message, 7)
    end

    -- 获取当前服务器名称
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    -- 确保当前服务器的数据结构存在
    if not RaidCalendarDB[currentServer] then
        RaidCalendarDB[currentServer] = { characters = {}, guildRaids = {} }
    end
    if not RaidCalendarDB[currentServer].guildRaids then
        RaidCalendarDB[currentServer].guildRaids = {}
    end

    -- 处理接收到的数据
    if actualMessage == "NO_RAID_LOCKS" then
        RaidCalendarDB[currentServer].guildRaids[sender] = {}
    else
        -- 新的消息格式：角色名,副本名,ID,职业,时间戳,重置时间
        for raidEntry in string.gmatch(actualMessage, "([^;]+)") do
            local parts = {}
            for part in string.gmatch(raidEntry, "([^,]+)") do
                table.insert(parts, part)
            end

            local characterName, raidName, raidId, className, readableTime, resetTime

            if table.getn(parts) >= 6 then
                -- 新格式：角色名,副本名,ID,职业,时间戳,重置时间
                characterName = parts[1]
                raidName = parts[2]
                raidId = tonumber(parts[3])
                className = parts[4]
                readableTime = tonumber(parts[5])
                resetTime = parts[6] or "未知"
            elseif table.getn(parts) >= 5 then
                -- 旧格式兼容：副本名,ID,职业,时间戳,重置时间（使用发送者作为角色名）
                characterName = sender
                raidName = parts[1]
                raidId = tonumber(parts[2])
                className = parts[3]
                readableTime = tonumber(parts[4])
                resetTime = parts[5] or "未知"
            end

            if characterName and raidName and raidId and readableTime then
                -- 按角色名组织数据（保存到当前服务器下）
                if not RaidCalendarDB[currentServer].guildRaids[characterName] then
                    RaidCalendarDB[currentServer].guildRaids[characterName] = {}
                end
                if not RaidCalendarDB[currentServer].guildRaids[characterName][raidName] then
                    RaidCalendarDB[currentServer].guildRaids[characterName][raidName] = {}
                end

                -- 移除旧的相同ID记录
                local newRaidList = {}
                for _, existingRaid in ipairs(RaidCalendarDB[currentServer].guildRaids[characterName][raidName]) do
                    if existingRaid.id ~= raidId then
                        table.insert(newRaidList, existingRaid)
                    end
                end

                -- 添加新记录
                table.insert(newRaidList, {
                    id = raidId,
                    class = className,
                    readableTime = readableTime,
                    resetTime = resetTime
                })

                RaidCalendarDB[currentServer].guildRaids[characterName][raidName] = newRaidList
            end
        end
    end
    
    -- 移除自动回复机制：用户可以通过打开界面或登录来主动同步数据
    -- 这样可以减少网络负担，避免不必要的消息交换
    
    -- 刷新界面
    if RaidCalendar.rightPanel then
        RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
    end
end

-- 测试函数：手动触发多角色同步（支持新的服务器分类结构）
function RaidCalendar_TestMultiCharSync()
    print("[测试] 开始多角色同步测试...")

    -- 保存当前角色数据
    RaidCalendar_SaveCurrentCharacterRaids()

    -- 获取当前服务器名称
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    print("当前服务器: " .. currentServer)

    -- 检查完整的本地数据
    local totalCount = 0

    -- 1. 检查自己的角色数据（当前服务器）
    if RaidCalendarDB and RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].characters then
        print("【自己的角色数据】")
        for char, data in pairs(RaidCalendarDB[currentServer].characters) do
            totalCount = totalCount + 1
            print("角色 " .. totalCount .. ": " .. char)
            if data.raids then
                for _, raid in pairs(data.raids) do
                    print("  副本: " .. (raid.name or "未知"))
                end
            end
        end
    end

    -- 2. 检查工会同步来的数据（当前服务器）
    if RaidCalendarDB and RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].guildRaids then
        print("【工会同步来的数据】")
        for char, data in pairs(RaidCalendarDB[currentServer].guildRaids) do
            totalCount = totalCount + 1
            print("角色 " .. totalCount .. ": " .. char)
            if type(data) == "table" then
                for raidName, raidList in pairs(data) do
                    if type(raidList) == "table" then
                        print("  副本: " .. raidName .. " (数量: " .. table.getn(raidList) .. ")")
                    end
                end
            end
        end
    end

    if totalCount == 0 then
        print("没有任何本地数据")
    else
        print("总共 " .. totalCount .. " 个角色的数据")
    end

    -- 发送同步消息
    RaidCalendar_SendSyncMessage("GUILD")
    print("[测试] 同步消息已发送")
end

-- 内存优化：高效的消息缓存清理
function RaidCalendar_CleanMessageCache()
    local limiter = RaidCalendar.globalSyncLimiter
    local currentTime = time()

    -- 内存优化：如果缓存未超过限制，跳过清理
    local cacheCount = 0
    for _ in pairs(limiter.messageCache) do
        cacheCount = cacheCount + 1
    end

    if cacheCount <= limiter.maxCacheSize then
        return
    end

    -- 内存优化：只在必要时进行清理，使用更高效的方法
    local cleanCache = {}
    local count = 0
    local maxAge = 180 -- 减少到3分钟

    for hash, timestamp in pairs(limiter.messageCache) do
        if currentTime - timestamp < maxAge and count < limiter.maxCacheSize then
            cleanCache[hash] = timestamp
            count = count + 1
        end
    end

    limiter.messageCache = cleanCache

    -- 内存优化：强制垃圾回收，释放内存（经典版WoW兼容）
    if cacheCount > limiter.maxCacheSize * 2 then
        collectgarbage()
    end
end

-- 修改：通用同步消息发送函数（发送所有本地角色数据）
function RaidCalendar_SendSyncMessage(channel)
    local currentPlayerName = GetUnitName("player")
    if not currentPlayerName then
        return false
    end

    -- 获取当前服务器名称
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    local message = ""

    if not RaidCalendarDB or not RaidCalendarDB[currentServer] or not RaidCalendarDB[currentServer].characters then
        message = "NO_RAID_LOCKS"
    else
        local allMessageParts = {}
        local hasAnyData = false

        -- 1. 处理自己的角色数据（当前服务器）
        for characterName, characterData in pairs(RaidCalendarDB[currentServer].characters) do
            if characterData and characterData.raids then
                local characterRaids = characterData.raids

                if next(characterRaids) ~= nil then
                    hasAnyData = true

                    -- 获取角色职业信息
                    local characterClass = "UNKNOWN"
                    if characterName == currentPlayerName then
                        local _, playerClassToken = UnitClass("player")
                        characterClass = playerClassToken or "UNKNOWN"
                    elseif characterData.class then
                        characterClass = characterData.class
                    end

                    -- 为每个角色的每个副本创建消息条目
                    for _, raidInfo in pairs(characterRaids) do
                        local name = raidInfo.name or "UnknownRaid"
                        local id = raidInfo.id or 0
                        local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                        local resetTime = raidInfo.resetTime or "未知"

                        -- 新消息格式：角色名,副本名,ID,职业,时间戳,重置时间
                        table.insert(allMessageParts, characterName .. "," .. name .. "," .. id .. "," .. characterClass .. "," .. readableTime .. "," .. resetTime)
                    end
                end
            end
        end

        -- 2. 处理工会同步来的角色数据（当前服务器）
        if RaidCalendarDB[currentServer].guildRaids then
            for characterName, characterGuildData in pairs(RaidCalendarDB[currentServer].guildRaids) do
                if type(characterGuildData) == "table" then
                    for raidName, raidEntries in pairs(characterGuildData) do
                        if type(raidEntries) == "table" then
                            for _, raidInfo in ipairs(raidEntries) do
                                if raidName and raidInfo.id then
                                    hasAnyData = true

                                    local characterClass = raidInfo.class or "UNKNOWN"
                                    local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                                    local resetTime = raidInfo.resetTime or "未知"

                                    -- 新消息格式：角色名,副本名,ID,职业,时间戳,重置时间
                                    table.insert(allMessageParts, characterName .. "," .. raidName .. "," .. raidInfo.id .. "," .. characterClass .. "," .. readableTime .. "," .. resetTime)
                                end
                            end
                        end
                    end
                end
            end
        end

        if hasAnyData then
            message = table.concat(allMessageParts, ";")
        else
            message = "NO_RAID_LOCKS"
        end
    end

    -- 使用简化的安全发送
    return RaidCalendar_SendMessageSafely(message, channel or "GUILD")
end

-- 简化：移除交换系统，不再需要回复冷却
function RaidCalendar_InitExchangeSystem()
    -- 保留函数以避免调用错误，但不再执行任何操作
end

-- 内存优化：针对副本同步的轻量级限制器，严格控制内存使用
RaidCalendar.globalSyncLimiter = {
    lastGlobalSyncTime = 0,
    globalSyncCooldown = 120, -- 全局2分钟内只允许一次同步
    loginSyncQueue = {},
    isProcessingQueue = false,

    -- 简化的防冲突机制
    lastSendTime = 0,
    sendCooldown = 30,         -- 个人发送冷却30秒

    -- 内存优化：消息去重缓存，严格限制大小
    messageCache = {},
    maxCacheSize = 10,         -- 减少到最多缓存10条消息指纹
    cacheCleanupInterval = 60  -- 每60秒清理一次缓存
}

-- 简化：安全发送消息（基本防冲突）
function RaidCalendar_SendMessageSafely(message, channel)
    if not message or not channel then
        return false
    end

    local currentTime = time()
    local limiter = RaidCalendar.globalSyncLimiter

    -- 检查发送冷却
    if currentTime - limiter.lastSendTime < limiter.sendCooldown then
        return false -- 还在冷却期
    end

    -- 检查消息是否重复
    local messageHash = string.len(message) .. "_" .. string.sub(message, 1, 10)
    if limiter.messageCache[messageHash] then
        return false -- 重复消息
    end

    -- 发送消息（支持长消息分割）
    if SendAddonMessage and RaidCalendar.MSG_PREFIX then
        local maxLength = 200  -- 保守的消息长度限制

        if string.len(message) <= maxLength then
            -- 短消息直接发送
            SendAddonMessage(RaidCalendar.MSG_PREFIX, message, channel)
        else
            -- 长消息分割发送
            local messageId = tostring(time()) .. "_" .. math.random(1000, 9999)
            local parts = {}
            local pos = 1

            while pos <= string.len(message) do
                local part = string.sub(message, pos, pos + maxLength - 1)
                table.insert(parts, part)
                pos = pos + maxLength
            end

            -- 发送消息头
            SendAddonMessage(RaidCalendar.MSG_PREFIX, "MULTIPART_START:" .. messageId .. ":" .. table.getn(parts), channel)

            -- 发送各部分
            for i, part in ipairs(parts) do
                SendAddonMessage(RaidCalendar.MSG_PREFIX, "MULTIPART_DATA:" .. messageId .. ":" .. i .. ":" .. part, channel)
            end

            -- 发送消息尾
            SendAddonMessage(RaidCalendar.MSG_PREFIX, "MULTIPART_END:" .. messageId, channel)
        end

        -- 更新记录
        limiter.lastSendTime = currentTime
        limiter.messageCache[messageHash] = currentTime

        -- 清理过期缓存
        RaidCalendar_CleanMessageCache()

        return true
    end

    return false
end

-- 新增：队列处理器启动
function RaidCalendar_StartQueueProcessor()
    if RaidCalendar.globalSyncLimiter.isProcessingQueue then
        return -- 已在处理中
    end

    RaidCalendar.globalSyncLimiter.isProcessingQueue = true

    local processorFrame = CreateFrame("Frame")
    local elapsed = 0

    processorFrame:SetScript("OnUpdate", function()
        elapsed = elapsed + arg1

        -- 每0.5秒检查一次队列
        if elapsed >= 0.5 then
            elapsed = 0

            local hasMessages = RaidCalendar_ProcessMessageQueues()

            -- 如果没有消息需要处理，停止处理器
            if not hasMessages then
                processorFrame:SetScript("OnUpdate", nil)
                RaidCalendar.globalSyncLimiter.isProcessingQueue = false
            end
        end
    end)
end

-- 内存优化：副本记录模块初始化函数，包含内存管理
function RaidCalendar_InitRaidRecordsModule()
    -- 强制清理错误的数据结构
    RaidCalendar_CleanupDataStructure()

    -- 执行过期数据清理
    RaidCalendar_AutoCleanupExpiredRaidData()

    -- 内存优化：启动定期内存清理
    RaidCalendar_StartMemoryCleanupTimer()
end

-- 内存优化：定期内存清理机制
function RaidCalendar_StartMemoryCleanupTimer()
    if RaidCalendar.memoryCleanupTimer then
        return -- 已经启动
    end

    RaidCalendar.memoryCleanupTimer = CreateFrame("Frame")
    local elapsed = 0
    local cleanupInterval = 300 -- 每5分钟清理一次

    RaidCalendar.memoryCleanupTimer:SetScript("OnUpdate", function()
        elapsed = elapsed + arg1
        if elapsed >= cleanupInterval then
            elapsed = 0

            -- 执行内存清理
            RaidCalendar_PerformMemoryCleanup()
        end
    end)
end

-- 内存优化：执行内存清理
function RaidCalendar_PerformMemoryCleanup()
    -- 清理消息缓存
    RaidCalendar_CleanMessageCache()

    -- 清理过期的副本数据
    RaidCalendar_AutoCleanupExpiredRaidData()

    -- 清理交换时间记录（只保留最近的）
    if RaidCalendar.lastExchangeTime then
        local currentTime = time()
        local cleanExchangeTime = {}
        for playerName, lastTime in pairs(RaidCalendar.lastExchangeTime) do
            if currentTime - lastTime < 3600 then -- 只保留1小时内的记录
                cleanExchangeTime[playerName] = lastTime
            end
        end
        RaidCalendar.lastExchangeTime = cleanExchangeTime
    end

    -- 强制垃圾回收（经典版WoW兼容）
    collectgarbage()
end

-- 新增：重新布局副本元素（保持展开状态）
function RaidCalendar_RelayoutRaidElements(parentFrame)
    if not parentFrame or not parentFrame.raidElements then
        return
    end
    
    -- 保存当前展开状态
    local savedStates = {}
    if parentFrame.raidGroupStates then
        for raidName, state in pairs(parentFrame.raidGroupStates) do
            savedStates[raidName] = state
        end
    end
    
    -- 重新布局
    RaidCalendar_UpdateRaidInfo(parentFrame)
    
    -- 恢复展开状态
    if parentFrame.raidGroupStates then
        for raidName, state in pairs(savedStates) do
            parentFrame.raidGroupStates[raidName] = state
        end
    end
    
    -- 根据保存的状态显示/隐藏详情框
    for _, element in ipairs(parentFrame.raidElements) do
        if element and element.raidName and element.detailFrame then
            if savedStates[element.raidName] then
                element.expanded = true
                element.detailFrame:Show()
                if element.expandIcon then
                    element.expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
                end
            else
                element.expanded = false
                element.detailFrame:Hide()
                if element.expandIcon then
                    element.expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
                end
            end
        end
    end
end

-- 新增：简化的数据哈希计算，用于检测变化
function RaidCalendar_GetDataHash()
    local playerName = GetUnitName("player")

    -- 获取当前服务器名称
    local currentServer = "Ravenstorm"  -- 默认值
    local realmName = GetRealmName()
    if realmName then
        if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
            currentServer = "Ravenstorm"
        elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
            currentServer = "Karazhan"
        elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
            currentServer = "Blood Ring"
        end
    end

    if not playerName or not RaidCalendarDB or not RaidCalendarDB[currentServer] or not RaidCalendarDB[currentServer].characters or not RaidCalendarDB[currentServer].characters[playerName] then
        return "empty"
    end

    local raids = RaidCalendarDB[currentServer].characters[playerName].raids
    if not raids or next(raids) == nil then
        return "empty"
    end
    
    -- 创建一个简单的数据指纹
    local hashParts = {}
    for _, raidInfo in pairs(raids) do
        table.insert(hashParts, (raidInfo.name or "") .. ":" .. (raidInfo.id or ""))
    end
    table.sort(hashParts)
    return table.concat(hashParts, "|")
end

-- 修改：只保留数据变化时的同步
function RaidCalendar_CheckDataChangeAndSync()
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not IsInGuild() then
        return
    end
    
    local currentHash = RaidCalendar_GetDataHash()
    
    -- 如果数据有变化，立即同步
    if currentHash ~= RaidCalendar.lastDataHash then
        -- 更新数据指纹
        RaidCalendar.lastDataHash = currentHash
        
        -- 执行同步
        RaidCalendar_SyncToGuild()
        
        -- 更新全局同步时间
        RaidCalendar.globalSyncLimiter.lastGlobalSyncTime = time()
    end
end



-- 修改：大幅优化登录同步，使用队列机制，减少消息输出
function RaidCalendar_RaidRecordsOnLogin()
    -- 减少消息输出
    
    -- 增加：检查基本函数是否可用
    local playerName = nil
    if GetUnitName then
        playerName = GetUnitName("player")
    elseif UnitName then
        playerName = UnitName("player")
    end
    
    if not playerName or playerName == "" then
        return
    end
    
    -- 执行过期数据清理（移除详细输出）
    RaidCalendar_AutoCleanupExpiredRaidData()
    
    -- 检查是否有白名单检查函数
    local currentCharacter = nil
    if type(RaidCalendar_GetCurrentCharacterName) == "function" then
        currentCharacter = RaidCalendar_GetCurrentCharacterName()
    else
        currentCharacter = playerName
    end
    
    -- 检查白名单
    local isInWhitelist = true
    if type(RaidCalendar_IsCharacterInWhitelist) == "function" then
        isInWhitelist = RaidCalendar_IsCharacterInWhitelist(currentCharacter)
    end
    
    if not isInWhitelist then
        return
    end
    
    -- 修改：使用队列机制延迟发送登录同步消息
    if IsInGuild then
        local inGuild = IsInGuild()
        if inGuild then
            -- 内存优化：大幅延迟登录同步，重用Frame对象
            local loginDelay = math.random(180, 600) -- 3-10分钟随机延迟

            -- 内存优化：重用全局登录同步Frame
            if not RaidCalendar.globalSyncLimiter.loginSyncFrame then
                RaidCalendar.globalSyncLimiter.loginSyncFrame = CreateFrame("Frame")
            end

            local loginSyncFrame = RaidCalendar.globalSyncLimiter.loginSyncFrame
            local loginElapsed = 0
            loginSyncFrame:SetScript("OnUpdate", function()
                loginElapsed = loginElapsed + arg1
                if loginElapsed >= loginDelay then
                    loginSyncFrame:SetScript("OnUpdate", nil)
                    
                    -- 保存当前角色副本数据
                    if type(RaidCalendar_SaveCurrentCharacterRaids) == "function" then
                        RaidCalendar_SaveCurrentCharacterRaids()
                    end
                    
                    -- 准备队列消息：发送所有本地数据（包括自己的和工会同步来的）
                    if playerName and RaidCalendarDB then
                        -- 获取当前服务器名称
                        local currentServer = "Ravenstorm"  -- 默认值
                        local realmName = GetRealmName()
                        if realmName then
                            if string.find(realmName, "Ravenstorm") or string.find(realmName, "拉风") then
                                currentServer = "Ravenstorm"
                            elseif string.find(realmName, "Karazhan") or string.find(realmName, "卡拉赞") then
                                currentServer = "Karazhan"
                            elseif string.find(realmName, "Blood") or string.find(realmName, "血环") then
                                currentServer = "Blood Ring"
                            end
                        end

                        local allMessageParts = {}
                        local hasAnyData = false

                        -- 1. 处理自己的角色数据（当前服务器）
                        if RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].characters then
                            for characterName, characterData in pairs(RaidCalendarDB[currentServer].characters) do
                                if characterData and characterData.raids then
                                    local characterRaids = characterData.raids

                                    if next(characterRaids) ~= nil then
                                        hasAnyData = true

                                        -- 获取角色职业信息
                                        local characterClass = "UNKNOWN"
                                        if characterName == playerName then
                                            local _, playerClassToken = UnitClass("player")
                                            characterClass = playerClassToken or "UNKNOWN"
                                        elseif characterData.class then
                                            characterClass = characterData.class
                                        end

                                        -- 为每个角色的每个副本创建消息条目
                                        for _, raidInfo in pairs(characterRaids) do
                                            local name = raidInfo.name or "UnknownRaid"
                                            local id = raidInfo.id or 0
                                            local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                                            local resetTime = raidInfo.resetTime or "未知"

                                            -- 新消息格式：角色名,副本名,ID,职业,时间戳,重置时间
                                            table.insert(allMessageParts, characterName .. "," .. name .. "," .. id .. "," .. characterClass .. "," .. readableTime .. "," .. resetTime)
                                        end
                                    end
                                end
                            end
                        end

                        -- 2. 处理工会同步来的角色数据（当前服务器）
                        if RaidCalendarDB[currentServer] and RaidCalendarDB[currentServer].guildRaids then
                            for characterName, characterGuildData in pairs(RaidCalendarDB[currentServer].guildRaids) do
                                if type(characterGuildData) == "table" then
                                    for raidName, raidEntries in pairs(characterGuildData) do
                                        if type(raidEntries) == "table" then
                                            for _, raidInfo in ipairs(raidEntries) do
                                                if raidName and raidInfo.id then
                                                    hasAnyData = true

                                                    local characterClass = raidInfo.class or "UNKNOWN"
                                                    local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                                                    local resetTime = raidInfo.resetTime or "未知"

                                                    -- 新消息格式：角色名,副本名,ID,职业,时间戳,重置时间
                                                    table.insert(allMessageParts, characterName .. "," .. raidName .. "," .. raidInfo.id .. "," .. characterClass .. "," .. readableTime .. "," .. resetTime)
                                                end
                                            end
                                        end
                                    end
                                end
                            end
                        end

                        local message = ""
                        if hasAnyData then
                            message = table.concat(allMessageParts, ";")
                        else
                            message = "NO_RAID_LOCKS"
                        end
                        
                        -- 加入同步队列而不是立即发送
                        if not RaidCalendar.globalSyncLimiter.loginSyncQueue then
                            RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                        end
                        
                        table.insert(RaidCalendar.globalSyncLimiter.loginSyncQueue, {
                            playerName = playerName,
                            message = message,
                            timestamp = time()
                        })
                        
                        -- 启动队列处理
                        RaidCalendar_ProcessLoginSyncQueue()
                    end
                    
                    -- 初始化数据哈希
                    if type(RaidCalendar_GetDataHash) == "function" then
                        RaidCalendar.lastDataHash = RaidCalendar_GetDataHash()
                    end
                end
            end)
        end
    end
end

-- 修改：副本记录模块的事件监听器，移除组队相关事件
local raidRecordEventFrame = CreateFrame("Frame", "RaidCalendarRaidRecordEventFrame")
raidRecordEventFrame:RegisterEvent("ADDON_LOADED")
raidRecordEventFrame:RegisterEvent("PLAYER_LOGIN")
raidRecordEventFrame:RegisterEvent("CHAT_MSG_ADDON")

-- 添加定期清理多部分消息缓存
local cleanupTimer = 0
local originalOnUpdate = raidRecordEventFrame:GetScript("OnUpdate")
raidRecordEventFrame:SetScript("OnUpdate", function()
    if originalOnUpdate then
        originalOnUpdate()
    end

    cleanupTimer = cleanupTimer + arg1
    if cleanupTimer >= MULTIPART_CONFIG.cleanupInterval then
        cleanupTimer = 0
        CleanupMultipartMessages()
    end
end)

-- 添加登录处理标记，防止重复执行
local loginProcessed = false

raidRecordEventFrame:SetScript("OnEvent", function()
    if event == "ADDON_LOADED" and arg1 == "RaidCalendar" then
        -- 安全初始化各个系统
        pcall(function()
            if type(RaidCalendar_InitExchangeSystem) == "function" then
                RaidCalendar_InitExchangeSystem()
            end
        end)

        pcall(function()
            if type(RaidCalendar_CleanupDataStructure) == "function" then
                RaidCalendar_CleanupDataStructure()
            end
        end)

        pcall(function()
            if type(RaidCalendar_InitRaidRecordsModule) == "function" then
                RaidCalendar_InitRaidRecordsModule()
            end
        end)

    elseif event == "PLAYER_LOGIN" and not loginProcessed then
        loginProcessed = true  -- 防止重复执行

        -- 延迟执行登录清理，避免与主模块冲突
        local loginDelayFrame = CreateFrame("Frame")
        local loginDelay = 0
        loginDelayFrame:SetScript("OnUpdate", function()
            loginDelay = loginDelay + arg1
            if loginDelay >= 1 then -- 延迟1秒执行
                loginDelayFrame:SetScript("OnUpdate", nil)

                -- 安全执行登录处理
                pcall(function()
                    if type(RaidCalendar_RaidRecordsOnLogin) == "function" then
                        RaidCalendar_RaidRecordsOnLogin()
                    end
                end)
            end
        end)
        
    elseif event == "CHAT_MSG_ADDON" and arg1 == RaidCalendar.MSG_PREFIX then
        -- 安全处理插件消息
        pcall(function()
            if type(RaidCalendar_ChatMsgAddonHandler) == "function" then
                RaidCalendar_ChatMsgAddonHandler(arg1, arg2, arg3, arg4)
            end
        end)
        
    end
end)
