<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/ ..\FrameXML\UI.xsd">
	<Frame name="RaidCalendarMainFrame" parent="UIParent" movable="true" enableMouse="true" hidden="true" frameStrata="FULLSCREEN_DIALOG" frameLevel="100">
		<Size>
			<AbsDimension x="625" y="680"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		
		<!-- 移除XML中的背景设置，改由Lua代码统一管理 -->
		<!--
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" tile="true">
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
		</Backdrop>
		-->
		
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="RaidCalendarTitle" inherits="GameFontNormalLarge">
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="-20"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				
				<FontString name="RaidCalendarContent" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP">
					<Size>
						<AbsDimension x="460" y="370"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="25" y="-50"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		
		<Frames>
			<Button name="RaidCalendarCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-5" y="-5"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						RaidCalendarMainFrame:Hide();
						-- 同时关闭右侧窗口但不重置状态
						if RaidCalendar and RaidCalendar.rightFrame then
							RaidCalendar.rightFrame:Hide();
						end
					</OnClick>
				</Scripts>
			</Button>
			
		</Frames>
		
		<Scripts>
			<OnLoad>
				RaidCalendar_OnLoad();
			</OnLoad>
			<OnEvent>
				RaidCalendar_OnEvent();
			</OnEvent>
			<OnDragStart>
				this:StartMoving();
			</OnDragStart>
			<OnDragStop>
				this:StopMovingOrSizing();
			</OnDragStop>
		</Scripts>
	</Frame>
</Ui>